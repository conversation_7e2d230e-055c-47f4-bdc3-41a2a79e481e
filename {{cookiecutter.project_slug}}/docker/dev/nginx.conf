upstream {{cookiecutter.project_slug}}_backend {
    server {{cookiecutter.project_slug}}_backend:8000;
}

server {
    listen 8080;
    server_name {{cookiecutter.domain_pattern}};
    client_max_body_size 20M;

    location / {
        proxy_pass http://{{cookiecutter.project_slug}}_backend;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $host;
        proxy_redirect off;
    }
}
