services:

  {{cookiecutter.project_slug}}_db:
    image: postgres:{{cookiecutter.postgres_version}}-alpine
    environment:
      POSTGRES_USER: {{cookiecutter.project_slug}}
      POSTGRES_PASSWORD: {{cookiecutter.project_slug}}
      POSTGRES_DB: {{cookiecutter.project_slug}}
    expose:
      - 5432
    volumes:
      - ../shared:/shared
      - {{cookiecutter.project_slug}}_postgres_data:/var/lib/postgresql/data/

  {{cookiecutter.project_slug}}_backend:
    container_name: {{cookiecutter.project_slug}}_backend
    build:
      context: ../..
      dockerfile: docker/dev/{{cookiecutter.project_slug}}_backend.Dockerfile
    image: {{cookiecutter.project_slug}}_backend
    stdin_open: true
    tty: true
    volumes:
      - ../..:/usr/src/{{cookiecutter.project_slug}}/
      - ipython_history:/root/.ipython
      - {{cookiecutter.project_slug}}_media:/usr/src/{{cookiecutter.project_slug}}/backend/django/media/
      - {{cookiecutter.project_slug}}_venv:/opt/venv
    ports:
      - 3003:8000
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      - {{cookiecutter.project_slug}}_db
    command: >
      sh -c 'python -Wa manage.py runserver 0.0.0.0:8000;'
{% if cookiecutter.use_nginx == 'y' %}
  {{cookiecutter.project_slug}}_ws:
    build:
      context: ../..
      dockerfile: docker/dev/{{cookiecutter.project_slug}}_ws.Dockerfile
    ports:
      - 80:8080
    depends_on:
      - {{cookiecutter.project_slug}}_backend
{% endif %}
volumes:
  ipython_history:
  {{cookiecutter.project_slug}}_media:
  {{cookiecutter.project_slug}}_postgres_data:
  {{cookiecutter.project_slug}}_venv:
