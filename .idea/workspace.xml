<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="9f8482ac-9a25-4eef-ac87-9c4179dd8817" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 0
}]]></component>
  <component name="ProjectId" id="2yFDbYDW2bzRTLxwOlu7TUrL106" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.OpenDjangoStructureViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "junie.onboarding.icon.badge.shown": "true",
    "last_opened_file_path": "/home/<USER>/Desktop/cookiecutter-django",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="cookiecutter-django" type="Python.DjangoServer" factoryName="Django server">
      <module name="cookiecutter-django" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="launchJavascriptDebuger" value="false" />
      <option name="port" value="8000" />
      <option name="host" value="" />
      <option name="additionalOptions" value="" />
      <option name="browserUrl" value="" />
      <option name="runTestServer" value="false" />
      <option name="runNoReload" value="false" />
      <option name="useCustomRunCommand" value="false" />
      <option name="customRunCommand" value="" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-PY-251.25410.159" />
        <option value="bundled-python-sdk-e0ed3721d81e-36ea0e71a18c-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.25410.159" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="9f8482ac-9a25-4eef-ac87-9c4179dd8817" name="Changes" comment="" />
      <created>1749421499558</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749421499558</updated>
      <workItem from="1749421500693" duration="143000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>