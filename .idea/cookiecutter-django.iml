<?xml version="1.0" encoding="UTF-8"?>
<module type="PYTHON_MODULE" version="4">
  <component name="FacetManager">
    <facet type="django" name="Django">
      <configuration>
        <option name="rootFolder" value="$MODULE_DIR$/{{cookiecutter.project_slug}}/backend/django" />
        <option name="settingsModule" value="settings.py" />
        <option name="manageScript" value="$MODULE_DIR$/{{cookiecutter.project_slug}}/backend/django/manage.py" />
        <option name="environment" value="&lt;map/&gt;" />
        <option name="doNotUseTestRunner" value="false" />
        <option name="trackFilePattern" value="migrations" />
      </configuration>
    </facet>
  </component>
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/{{cookiecutter.project_slug}}/backend/django" isTestSource="false" />
      <excludeFolder url="file://$MODULE_DIR$/.venv" />
    </content>
    <orderEntry type="jdk" jdkName="Python 3.12 (cookiecutter-django)" jdkType="Python SDK" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
  <component name="PyDocumentationSettings">
    <option name="format" value="PLAIN" />
    <option name="myDocStringFormat" value="Plain" />
  </component>
</module>