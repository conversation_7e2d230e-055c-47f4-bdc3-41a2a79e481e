#!/usr/bin/env python3
"""
Comprehensive test for Django project creation using cookiecutter template.

This test validates the complete Django project creation process including:
1. Project generation using cookiecutter
2. Docker environment setup
3. Django development server startup
4. Admin superuser creation
5. Admin interface accessibility verification
6. Proper cleanup of resources
"""

import os0
import sys
import time
import shutil
import tempfile
import subprocess
import requests


class DjangoProjectTest:
    """Test class for validating Django project creation and functionality."""
    
    def __init__(self, project_name="blog"):
        self.project_name = project_name
        self.test_dir = None
        self.project_dir = None
        self.containers_started = False
        
    def setup_test_environment(self):
        """Setup temporary test directory and environment."""
        print("Setting up test environment...")
        self.test_dir = tempfile.mkdtemp(prefix=f"django_test_{self.project_name}_")
        print(f"Test directory: {self.test_dir}")
        
    def generate_django_project_direct(self):
        """Generate Django project by copying and processing template files."""
        print("Generating Django project by processing template...")

        # Get th0e template directory (current working directory)
        template_dir = os.getcwd()
        template_project_dir = os.path.join(template_dir, "{{cookiecutter.project_slug}}")

        if not os.path.exists(template_project_dir):
            raise Exception(f"Template directory not found: {template_project_dir}")

        # Create project directory
        self.project_dir = os.path.join(self.test_dir, self.project_name)

        # Copy template and replace variables
        self._copy_and_process_template(template_project_dir, self.project_dir)

        print(f"Project generated successfully at: {self.project_dir}")

    def _copy_and_process_template(self, src_dir, dst_dir):
        """Copy template directory and replace cookiecutter variables."""

        def replace_variables(text):
            """Replace cookiecutter variables in text."""
            # Simple replacements
            text = text.replace('{{cookiecutter.project_name}}', 'Blog Project')
            text = text.replace('{{cookiecutter.project_slug}}', self.project_name)
            text = text.replace('{{cookiecutter.domain_pattern}}', f'*.{self.project_name}.localhost')
            text = text.replace('{{cookiecutter.django_version}}', '5.1')
            text = text.replace('{{cookiecutter.python_version}}', '3.12')
            text = text.replace('{{cookiecutter.postgres_version}}', '16')
            text = text.replace('{{cookiecutter.use_nginx}}', 'y')

            # Handle uppercase
            text = text.replace('{{cookiecutter.project_slug.upper()}}', self.project_name.upper())

            # Handle title case for app config
            title_name = self.project_name.title().replace('_', '')
            text = text.replace('{{cookiecutter.project_slug.title().replace(\'_\', \'\')}}', title_name)

            # Handle conditional blocks - remove the template syntax and keep content
            if '{% if cookiecutter.use_nginx == \'y\' %}' in text:
                text = text.replace('{% if cookiecutter.use_nginx == \'y\' %}', '')
                text = text.replace('{% endif %}', '')

            return text

        # Create destination directory
        os.makedirs(dst_dir, exist_ok=True)

        # Copy files and directories
        for root, _, files in os.walk(src_dir):
            # Calculate relative path
            rel_path = os.path.relpath(root, src_dir)

            # Replace variables in directory names
            if rel_path != '.':
                rel_path = replace_variables(rel_path)
                dst_root = os.path.join(dst_dir, rel_path)
            else:
                dst_root = dst_dir

            os.makedirs(dst_root, exist_ok=True)

            # Copy files
            for file in files:
                src_file = os.path.join(root, file)

                # Replace variables in filename
                dst_filename = replace_variables(file)
                dst_file = os.path.join(dst_root, dst_filename)

                # Read, process, and write file
                try:
                    with open(src_file, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # Replace variables in content
                    content = replace_variables(content)

                    with open(dst_file, 'w', encoding='utf-8') as f:
                        f.write(content)

                except UnicodeDecodeError:
                    # Binary file, copy as-is
                    shutil.copy2(src_file, dst_file)

                # Make shell scripts executable
                if dst_filename.endswith('.sh'):
                    os.chmod(dst_file, 0o755)

        
    def setup_docker_environment(self):
        """Setup and start Docker environment."""
        print("Setting up Docker environment...")
        
        # Change to project directory
        original_cwd = os.getcwd()
        os.chdir(self.project_dir)
        
        try:
            # Build and start containers
            compose_file = "docker/dev/compose.yml"
            
            if not os.path.exists(compose_file):
                raise Exception(f"Docker compose file not found: {compose_file}")
            
            print("Building Docker containers...")
            build_cmd = ["docker", "compose", "-f", compose_file, "build"]
            result = subprocess.run(build_cmd, capture_output=True, text=True, timeout=300)

            if result.returncode != 0:
                raise Exception(f"Docker build failed: {result.stderr}")

            print("Starting Docker containers...")
            up_cmd = ["docker", "compose", "-f", compose_file, "up", "-d"]
            result = subprocess.run(up_cmd, capture_output=True, text=True, timeout=120)
            
            if result.returncode != 0:
                raise Exception(f"Docker up failed: {result.stderr}")
                
            self.containers_started = True
            print("Docker containers started successfully")
            
            # Wait for services to be ready
            self.wait_for_services()
            
        finally:
            os.chdir(original_cwd)
            
    def wait_for_services(self):
        """Wait for Docker services to be ready."""
        print("Waiting for services to be ready...")
        
        # Wait for database to be ready
        max_attempts = 30
        for attempt in range(max_attempts):
            try:
                # Check if backend container is running and database is accessible
                cmd = [
                    "docker", "exec", f"{self.project_name}_backend",
                    "python", "manage.py", "check", "--database", "default"
                ]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    print("Database is ready!")
                    break
                    
            except subprocess.TimeoutExpired:
                pass
                
            print(f"Waiting for database... (attempt {attempt + 1}/{max_attempts})")
            time.sleep(2)
        else:
            raise Exception("Database failed to become ready within timeout")
            
    def run_django_migrations(self):
        """Run Django database migrations."""
        print("Running Django migrations...")
        
        cmd = [
            "docker", "exec", f"{self.project_name}_backend",
            "python", "manage.py", "migrate"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode != 0:
            raise Exception(f"Django migrations failed: {result.stderr}")
            
        print("Django migrations completed successfully")
        
    def create_superuser(self):
        """Create Django admin superuser."""
        print("Creating Django superuser...")
        
        # Create superuser using environment variables
        env = os.environ.copy()
        env.update({
            'DJANGO_SUPERUSER_USERNAME': 'admin',
            'DJANGO_SUPERUSER_EMAIL': '<EMAIL>',
            'DJANGO_SUPERUSER_PASSWORD': 'admin123'
        })
        
        cmd = [
            "docker", "exec", "-e", "DJANGO_SUPERUSER_USERNAME=admin",
            "-e", "DJANGO_SUPERUSER_EMAIL=<EMAIL>",
            "-e", "DJANGO_SUPERUSER_PASSWORD=admin123",
            f"{self.project_name}_backend",
            "python", "manage.py", "createsuperuser", "--noinput"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode != 0:
            raise Exception(f"Superuser creation failed: {result.stderr}")
            
        print("Django superuser created successfully")
        
    def verify_admin_interface(self):
        """Verify Django admin interface is accessible."""
        print("Verifying Django admin interface...")
        
        # Determine the admin URL based on nginx configuration
        if self.containers_started:
            # Try nginx URL first (if nginx is enabled)
            admin_urls = [
                f"http://backend.{self.project_name}.localhost/admin/",
                "http://localhost/admin/",
                "http://localhost:3003/admin/"  # Direct backend access
            ]
        else:
            admin_urls = ["http://localhost:3003/admin/"]
            
        success = False
        for url in admin_urls:
            try:
                print(f"Trying admin URL: {url}")
                response = requests.get(url, timeout=10)
                
                if response.status_code == 200:
                    if "Django administration" in response.text or "admin" in response.text.lower():
                        print(f"✓ Admin interface accessible at: {url}")
                        success = True
                        break
                    else:
                        print(f"✗ URL responded but doesn't appear to be admin interface: {url}")
                else:
                    print(f"✗ HTTP {response.status_code} from {url}")
                    
            except requests.exceptions.RequestException as e:
                print(f"✗ Failed to connect to {url}: {e}")
                
        if not success:
            raise Exception("Django admin interface is not accessible at any expected URL")
            
        print("Django admin interface verification completed successfully")

    def cleanup_resources(self):
        """Clean up Docker containers and test files."""
        print("Cleaning up resources...")

        if self.containers_started and self.project_dir:
            # Stop and remove containers
            original_cwd = os.getcwd()
            os.chdir(self.project_dir)

            try:
                compose_file = "docker/dev/compose.yml"

                print("Stopping Docker containers...")
                down_cmd = ["docker", "compose", "-f", compose_file, "down", "-v", "--remove-orphans"]
                subprocess.run(down_cmd, capture_output=True, text=True, timeout=60)

                # Remove images
                print("Removing Docker images...")
                rmi_cmd = ["docker", "rmi", f"{self.project_name}_backend", "-f"]
                subprocess.run(rmi_cmd, capture_output=True, text=True, timeout=30)

            except Exception as e:
                print(f"Warning: Error during Docker cleanup: {e}")
            finally:
                os.chdir(original_cwd)

        # Remove test directory
        if self.test_dir and os.path.exists(self.test_dir):
            try:
                shutil.rmtree(self.test_dir)
                print(f"Test directory removed: {self.test_dir}")
            except Exception as e:
                print(f"Warning: Could not remove test directory: {e}")

        print("Cleanup completed")

    def run_full_test(self):
        """Run the complete Django project test."""
        print(f"Starting Django project test for '{self.project_name}'")
        print("=" * 60)

        try:
            # Setup and project generation
            self.setup_test_environment()
            self.generate_django_project_direct()

            # Docker environment and Django setup
            self.setup_docker_environment()
            self.run_django_migrations()
            self.create_superuser()

            # Verification
            self.verify_admin_interface()

            print("=" * 60)
            print("✓ All tests passed successfully!")
            print(f"✓ Django project '{self.project_name}' created and verified")
            print("✓ Docker environment setup and running")
            print("✓ Database migrations completed")
            print("✓ Admin superuser created")
            print("✓ Admin interface accessible and responding")

            return True

        except Exception as e:
            print("=" * 60)
            print(f"✗ Test failed: {e}")
            return False

        finally:
            self.cleanup_resources()


def install_dependencies():
    """Install required dependencies for the test."""
    print("Installing test dependencies...")

    dependencies = [
        "requests"
    ]

    for dep in dependencies:
        try:
            print(f"Installing {dep}...")
            subprocess.run([sys.executable, "-m", "pip", "install", dep],
                         check=True, capture_output=True, text=True)
        except subprocess.CalledProcessError as e:
            print(f"Failed to install {dep}: {e}")
            return False

    # Check if docker compose is available
    try:
        subprocess.run(["docker", "compose", "version"],
                      check=True, capture_output=True, text=True)
        print("Docker Compose is available")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("Warning: docker compose not found. Please install Docker and Docker Compose.")
        return False

    print("Dependencies installed successfully")
    return True


def main():
    """Main test execution function."""
    print("Django Cookiecutter Template Test")
    print("=" * 60)

    # Install dependencies
    if not install_dependencies():
        print("Failed to install dependencies. Exiting.")
        sys.exit(1)

    # Run the test
    test = DjangoProjectTest("blog")
    success = test.run_full_test()

    if success:
        print("\n🎉 Test completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Test failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
