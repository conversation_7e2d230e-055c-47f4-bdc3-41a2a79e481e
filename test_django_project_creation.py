#!/usr/bin/env python3
"""
Django project creation test using cookiecutter template.
Tests project generation, Docker setup, and admin interface.
"""

import os
import sys
import time
import shutil
import tempfile
import venv
import importlib
import requests


class DjangoProjectTest:
    """Test class for Django project creation and functionality."""

    def __init__(self, project_name="blog"):
        self.project_name = project_name
        self.test_dir = None
        self.project_dir = None
        self.venv_dir = None
        self.venv_python = None
        self.containers_started = False

    def setup_test_environment(self):
        """Setup test directory and virtual environment."""
        print("Setting up test environment...")
        self.test_dir = tempfile.mkdtemp(prefix=f"django_test_{self.project_name}_")
        print(f"Test directory: {self.test_dir}")
        self.setup_virtual_environment()

    def setup_virtual_environment(self):
        """Create and setup virtual environment."""
        print("Creating virtual environment...")
        self.venv_dir = os.path.join(self.test_dir, "venv")
        venv.create(self.venv_dir, with_pip=True)

        # Linux/macOS path
        self.venv_python = os.path.join(self.venv_dir, "bin", "python")
        print(f"Virtual environment created at: {self.venv_dir}")

        # Install dependencies
        self.install_venv_dependencies()

    def install_venv_dependencies(self):
        """Install required dependencies in virtual environment."""
        print("Installing dependencies in virtual environment...")

        dependencies = ["cookiecutter", "requests"]

        for dep in dependencies:
            print(f"Installing {dep}...")
            self._run_pip_install(dep)

        print("Dependencies installed successfully")

    def _run_pip_install(self, package):
        """Install package using pip in virtual environment."""
        import subprocess

        cmd = [self.venv_python, "-m", "pip", "install", package]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)

        if result.returncode != 0:
            raise Exception(f"Failed to install {package}: {result.stderr}")

    def _import_from_venv(self, module_name):
        """Import module from virtual environment."""
        site_packages = os.path.join(self.venv_dir, "lib", f"python{sys.version_info.major}.{sys.version_info.minor}", "site-packages")

        if site_packages not in sys.path:
            sys.path.insert(0, site_packages)

        try:
            return importlib.import_module(module_name)
        except ImportError as e:
            raise ImportError(f"Could not import {module_name}: {e}")
        
    def generate_django_project_with_cookiecutter(self):
        """Generate Django project using cookiecutter."""
        print("Generating Django project using cookiecutter...")

        try:
            cookiecutter = self._import_from_venv('cookiecutter.main')
            template_dir = os.getcwd()

            context = {
                'project_name': 'Blog Project',
                'project_slug': self.project_name,
                'domain_pattern': f'*.{self.project_name}.localhost',
                'django_version': '5.1',
                'python_version': '3.12',
                'postgres_version': '16',
                'use_nginx': 'y'
            }

            self.project_dir = cookiecutter.cookiecutter(
                template_dir,
                output_dir=self.test_dir,
                no_input=True,
                extra_context=context
            )

            # Fix permissions for shell scripts
            self._fix_script_permissions()

            print(f"Project generated successfully at: {self.project_dir}")

        except Exception as e:
            print(f"Cookiecutter generation failed: {e}")
            print("Falling back to manual template processing...")
            self.generate_django_project_manual()

    def _fix_script_permissions(self):
        """Fix permissions for shell scripts in the generated project."""
        if not self.project_dir:
            return

        # Find all .sh files and make them executable
        for root, _, files in os.walk(self.project_dir):
            for file in files:
                if file.endswith('.sh'):
                    script_path = os.path.join(root, file)
                    os.chmod(script_path, 0o755)
                    print(f"Fixed permissions for: {script_path}")

    def generate_django_project_manual(self):
        """Fallback: Generate Django project by manual template processing."""
        print("Generating Django project by manual processing...")

        template_dir = os.getcwd()
        template_project_dir = os.path.join(template_dir, "{{cookiecutter.project_slug}}")

        if not os.path.exists(template_project_dir):
            raise Exception(f"Template directory not found: {template_project_dir}")

        self.project_dir = os.path.join(self.test_dir, self.project_name)
        self._copy_and_process_template(template_project_dir, self.project_dir)
        self._fix_script_permissions()

        print(f"Project generated successfully at: {self.project_dir}")

    def _copy_and_process_template(self, src_dir, dst_dir):
        """Copy template directory and replace cookiecutter variables."""

        def replace_variables(text):
            """Replace cookiecutter variables in text."""
            replacements = {
                '{{cookiecutter.project_name}}': 'Blog Project',
                '{{cookiecutter.project_slug}}': self.project_name,
                '{{cookiecutter.domain_pattern}}': f'*.{self.project_name}.localhost',
                '{{cookiecutter.django_version}}': '5.1',
                '{{cookiecutter.python_version}}': '3.12',
                '{{cookiecutter.postgres_version}}': '16',
                '{{cookiecutter.use_nginx}}': 'y',
                '{{cookiecutter.project_slug.upper()}}': self.project_name.upper(),
                '{{cookiecutter.project_slug.title().replace(\'_\', \'\')}}': self.project_name.title().replace('_', ''),
                '{% if cookiecutter.use_nginx == \'y\' %}': '',
                '{% endif %}': ''
            }

            for old, new in replacements.items():
                text = text.replace(old, new)
            return text

        os.makedirs(dst_dir, exist_ok=True)

        # Copy files and directories
        for root, _, files in os.walk(src_dir):
            rel_path = os.path.relpath(root, src_dir)

            if rel_path != '.':
                rel_path = replace_variables(rel_path)
                dst_root = os.path.join(dst_dir, rel_path)
            else:
                dst_root = dst_dir

            os.makedirs(dst_root, exist_ok=True)

            for file in files:
                src_file = os.path.join(root, file)
                dst_filename = replace_variables(file)
                dst_file = os.path.join(dst_root, dst_filename)

                try:
                    with open(src_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    content = replace_variables(content)
                    with open(dst_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                except UnicodeDecodeError:
                    shutil.copy2(src_file, dst_file)


    def setup_docker_environment(self):
        """Setup and start Docker environment."""
        print("Setting up Docker environment...")

        original_cwd = os.getcwd()
        os.chdir(self.project_dir)

        try:
            compose_file = "docker/dev/compose.yml"

            if not os.path.exists(compose_file):
                raise Exception(f"Docker compose file not found: {compose_file}")

            print("Building and starting Docker containers...")
            self._run_docker_compose_commands()

            self.containers_started = True
            print("Docker containers started successfully")

            self.wait_for_services()

        finally:
            os.chdir(original_cwd)

    def _run_docker_compose_commands(self):
        """Run docker-compose commands."""
        import subprocess

        compose_file = "docker/dev/compose.yml"

        print("Building Docker containers...")
        build_cmd = ["docker", "compose", "-f", compose_file, "build"]
        result = subprocess.run(build_cmd, capture_output=True, text=True, timeout=300)

        if result.returncode != 0:
            raise Exception(f"Docker build failed: {result.stderr}")

        print("Starting Docker containers...")
        up_cmd = ["docker", "compose", "-f", compose_file, "up", "-d"]
        result = subprocess.run(up_cmd, capture_output=True, text=True, timeout=120)

        if result.returncode != 0:
            raise Exception(f"Docker up failed: {result.stderr}")
            
    def wait_for_services(self):
        """Wait for Docker services to be ready."""
        print("Waiting for services to be ready...")

        max_attempts = 30
        for attempt in range(max_attempts):
            try:
                self._run_docker_exec_command(["python", "manage.py", "check", "--database", "default"])
                print("Database is ready!")
                break
            except Exception:
                pass

            print(f"Waiting for database... (attempt {attempt + 1}/{max_attempts})")
            time.sleep(2)
        else:
            raise Exception("Database failed to become ready within timeout")

    def run_django_migrations(self):
        """Run Django database migrations."""
        print("Running Django migrations...")

        try:
            self._run_docker_exec_command(["python", "manage.py", "migrate"])
            print("Django migrations completed successfully")
        except Exception as e:
            raise Exception(f"Django migrations failed: {e}")

    def create_superuser(self):
        """Create Django admin superuser."""
        print("Creating Django superuser...")

        try:
            env_vars = {
                'DJANGO_SUPERUSER_USERNAME': 'admin',
                'DJANGO_SUPERUSER_EMAIL': '<EMAIL>',
                'DJANGO_SUPERUSER_PASSWORD': 'admin123'
            }

            self._run_docker_exec_command(["python", "manage.py", "createsuperuser", "--noinput"], env_vars)
            print("Django superuser created successfully")
        except Exception as e:
            raise Exception(f"Superuser creation failed: {e}")

    def _run_docker_exec_command(self, command, env_vars=None):
        """Execute a command in the Docker container."""
        import subprocess

        cmd = ["docker", "exec"]

        if env_vars:
            for key, value in env_vars.items():
                cmd.extend(["-e", f"{key}={value}"])

        cmd.append(f"{self.project_name}_backend")
        cmd.extend(command)

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

        if result.returncode != 0:
            raise Exception(f"Command failed: {result.stderr}")

        return result.stdout
        
    def verify_admin_interface(self):
        """Verify Django admin interface is accessible."""
        print("Verifying Django admin interface...")

        admin_urls = [
            f"http://backend.{self.project_name}.localhost/admin/",
            "http://localhost/admin/",
            "http://localhost:3003/admin/"
        ]

        for url in admin_urls:
            try:
                print(f"Trying admin URL: {url}")
                response = requests.get(url, timeout=10)

                if response.status_code == 200 and ("Django administration" in response.text or "admin" in response.text.lower()):
                    print(f"✓ Admin interface accessible at: {url}")
                    print("Django admin interface verification completed successfully")
                    return
                else:
                    print(f"✗ HTTP {response.status_code} from {url}")

            except requests.exceptions.RequestException as e:
                print(f"✗ Failed to connect to {url}: {e}")

        raise Exception("Django admin interface is not accessible at any expected URL")

    def cleanup_resources(self):
        """Clean up Docker containers and test files."""
        print("Cleaning up resources...")

        if self.containers_started and self.project_dir:
            original_cwd = os.getcwd()
            os.chdir(self.project_dir)

            try:
                self._cleanup_docker_containers()
            except Exception as e:
                print(f"Warning: Error during Docker cleanup: {e}")
            finally:
                os.chdir(original_cwd)

        if self.test_dir and os.path.exists(self.test_dir):
            try:
                shutil.rmtree(self.test_dir)
                print(f"Test directory removed: {self.test_dir}")
            except Exception as e:
                print(f"Warning: Could not remove test directory: {e}")

        print("Cleanup completed")

    def _cleanup_docker_containers(self):
        """Clean up Docker containers and images."""
        import subprocess

        compose_file = "docker/dev/compose.yml"

        print("Stopping Docker containers...")
        down_cmd = ["docker", "compose", "-f", compose_file, "down", "-v", "--remove-orphans"]
        subprocess.run(down_cmd, capture_output=True, text=True, timeout=60)

        print("Removing Docker images...")
        rmi_cmd = ["docker", "rmi", f"{self.project_name}_backend", "-f"]
        subprocess.run(rmi_cmd, capture_output=True, text=True, timeout=30)

    def run_full_test(self):
        """Run the complete Django project test."""
        print(f"Starting Django project test for '{self.project_name}'")
        print("=" * 60)

        try:
            self.setup_test_environment()
            self.generate_django_project_with_cookiecutter()
            self.setup_docker_environment()
            self.run_django_migrations()
            self.create_superuser()
            self.verify_admin_interface()

            print("=" * 60)
            print("✓ All tests passed successfully!")
            print(f"✓ Django project '{self.project_name}' created and verified")
            print("✓ Virtual environment created and configured")
            print("✓ Docker environment setup and running")
            print("✓ Database migrations completed")
            print("✓ Admin superuser created")
            print("✓ Admin interface accessible and responding")

            return True

        except Exception as e:
            print("=" * 60)
            print(f"✗ Test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

        finally:
            self.cleanup_resources()


def check_docker_availability():
    """Check if Docker and Docker Compose are available."""
    import subprocess

    try:
        subprocess.run(["docker", "compose", "version"], check=True, capture_output=True, text=True)
        print("Docker Compose is available")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("Warning: docker compose not found. Please install Docker and Docker Compose.")
        return False


def main():
    """Main test execution function."""
    print("Django Cookiecutter Template Test")
    print("=" * 60)

    if not check_docker_availability():
        print("Docker is required for this test. Exiting.")
        sys.exit(1)

    test = DjangoProjectTest("blog")
    success = test.run_full_test()

    if success:
        print("\n🎉 Test completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Test failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
