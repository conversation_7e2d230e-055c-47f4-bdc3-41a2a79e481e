#!/usr/bin/env python3
"""
Comprehensive test for Django project creation using cookiecutter template.

This test validates the complete Django project creation process including:
1. Virtual environment creation and setup
2. Project generation using cookiecutter
3. Docker environment setup
4. Django development server startup
5. Admin superuser creation
6. Admin interface accessibility verification
7. Proper cleanup of resources
"""

import os
import sys
import time
import shutil
import tempfile
import venv
import importlib
import requests


class DjangoProjectTest:
    """Test class for validating Django project creation and functionality."""

    def __init__(self, project_name="blog"):
        self.project_name = project_name
        self.test_dir = None
        self.project_dir = None
        self.venv_dir = None
        self.venv_python = None
        self.containers_started = False

    def setup_test_environment(self):
        """Setup temporary test directory and virtual environment."""
        print("Setting up test environment...")
        self.test_dir = tempfile.mkdtemp(prefix=f"django_test_{self.project_name}_")
        print(f"Test directory: {self.test_dir}")

        # Setup virtual environment
        self.setup_virtual_environment()

    def setup_virtual_environment(self):
        """Create and setup virtual environment for the test."""
        print("Creating virtual environment...")
        self.venv_dir = os.path.join(self.test_dir, "venv")

        # Create virtual environment
        venv.create(self.venv_dir, with_pip=True)

        # Determine Python executable path in venv
        if os.name == 'nt':  # Windows
            self.venv_python = os.path.join(self.venv_dir, "Scripts", "python.exe")
        else:  # Unix/Linux/macOS
            self.venv_python = os.path.join(self.venv_dir, "bin", "python")

        print(f"Virtual environment created at: {self.venv_dir}")
        print(f"Virtual environment Python: {self.venv_python}")

        # Install required dependencies in virtual environment
        self.install_venv_dependencies()

    def install_venv_dependencies(self):
        """Install required dependencies in the virtual environment."""
        print("Installing dependencies in virtual environment...")

        dependencies = [
            "cookiecutter",
            "requests"
        ]

        for dep in dependencies:
            print(f"Installing {dep}...")
            self._run_pip_install(dep)

        print("Dependencies installed successfully in virtual environment")

    def _run_pip_install(self, package):
        """Install a package using pip in the virtual environment."""
        import subprocess

        cmd = [self.venv_python, "-m", "pip", "install", package]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)

        if result.returncode != 0:
            raise Exception(f"Failed to install {package}: {result.stderr}")

    def _import_from_venv(self, module_name):
        """Import a module from the virtual environment."""
        # Add venv site-packages to Python path temporarily
        if os.name == 'nt':
            site_packages = os.path.join(self.venv_dir, "Lib", "site-packages")
        else:
            site_packages = os.path.join(self.venv_dir, "lib", f"python{sys.version_info.major}.{sys.version_info.minor}", "site-packages")

        if site_packages not in sys.path:
            sys.path.insert(0, site_packages)

        try:
            return importlib.import_module(module_name)
        except ImportError as e:
            raise ImportError(f"Could not import {module_name} from virtual environment: {e}")
        
    def generate_django_project_with_cookiecutter(self):
        """Generate Django project using cookiecutter library."""
        print("Generating Django project using cookiecutter...")

        try:
            # Import cookiecutter from virtual environment
            cookiecutter = self._import_from_venv('cookiecutter.main')

            # Get current directory as template
            template_dir = os.getcwd()

            # Define cookiecutter context
            context = {
                'project_name': 'Blog Project',
                'project_slug': self.project_name,
                'domain_pattern': f'*.{self.project_name}.localhost',
                'django_version': '5.1',
                'python_version': '3.12',
                'postgres_version': '16',
                'use_nginx': 'y'
            }

            # Generate project using cookiecutter
            self.project_dir = cookiecutter.cookiecutter(
                template_dir,
                output_dir=self.test_dir,
                no_input=True,
                extra_context=context
            )

            print(f"Project generated successfully at: {self.project_dir}")

        except Exception as e:
            print(f"Cookiecutter generation failed: {e}")
            print("Falling back to manual template processing...")
            self.generate_django_project_manual()

    def generate_django_project_manual(self):
        """Fallback method: Generate Django project by manual template processing."""
        print("Generating Django project by manual processing...")

        # Get template directory (current working directory)
        template_dir = os.getcwd()
        template_project_dir = os.path.join(template_dir, "{{cookiecutter.project_slug}}")

        if not os.path.exists(template_project_dir):
            raise Exception(f"Template directory not found: {template_project_dir}")

        # Create project directory
        self.project_dir = os.path.join(self.test_dir, self.project_name)

        # Copy template and replace variables
        self._copy_and_process_template(template_project_dir, self.project_dir)

        print(f"Project generated successfully at: {self.project_dir}")

    def _copy_and_process_template(self, src_dir, dst_dir):
        """Copy template directory and replace cookiecutter variables."""

        def replace_variables(text):
            """Replace cookiecutter variables in text."""
            # Simple replacements
            text = text.replace('{{cookiecutter.project_name}}', 'Blog Project')
            text = text.replace('{{cookiecutter.project_slug}}', self.project_name)
            text = text.replace('{{cookiecutter.domain_pattern}}', f'*.{self.project_name}.localhost')
            text = text.replace('{{cookiecutter.django_version}}', '5.1')
            text = text.replace('{{cookiecutter.python_version}}', '3.12')
            text = text.replace('{{cookiecutter.postgres_version}}', '16')
            text = text.replace('{{cookiecutter.use_nginx}}', 'y')

            # Handle uppercase
            text = text.replace('{{cookiecutter.project_slug.upper()}}', self.project_name.upper())

            # Handle title case for app config
            title_name = self.project_name.title().replace('_', '')
            text = text.replace('{{cookiecutter.project_slug.title().replace(\'_\', \'\')}}', title_name)

            # Handle conditional blocks - remove the template syntax and keep content
            if '{% if cookiecutter.use_nginx == \'y\' %}' in text:
                text = text.replace('{% if cookiecutter.use_nginx == \'y\' %}', '')
                text = text.replace('{% endif %}', '')

            return text

        # Create destination directory
        os.makedirs(dst_dir, exist_ok=True)

        # Copy files and directories
        for root, _, files in os.walk(src_dir):
            # Calculate relative path
            rel_path = os.path.relpath(root, src_dir)

            # Replace variables in directory names
            if rel_path != '.':
                rel_path = replace_variables(rel_path)
                dst_root = os.path.join(dst_dir, rel_path)
            else:
                dst_root = dst_dir

            os.makedirs(dst_root, exist_ok=True)

            # Copy files
            for file in files:
                src_file = os.path.join(root, file)

                # Replace variables in filename
                dst_filename = replace_variables(file)
                dst_file = os.path.join(dst_root, dst_filename)

                # Read, process, and write file
                try:
                    with open(src_file, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # Replace variables in content
                    content = replace_variables(content)

                    with open(dst_file, 'w', encoding='utf-8') as f:
                        f.write(content)

                except UnicodeDecodeError:
                    # Binary file, copy as-is
                    shutil.copy2(src_file, dst_file)

                # Make shell scripts executable
                if dst_filename.endswith('.sh'):
                    os.chmod(dst_file, 0o755)


    def setup_docker_environment(self):
        """Setup and start Docker environment using Docker SDK."""
        print("Setting up Docker environment...")

        try:
            # Change to project directory
            original_cwd = os.getcwd()
            os.chdir(self.project_dir)

            try:
                compose_file = "docker/dev/compose.yml"

                if not os.path.exists(compose_file):
                    raise Exception(f"Docker compose file not found: {compose_file}")

                # For now, fall back to subprocess for docker-compose as Docker SDK doesn't have direct compose support
                print("Building and starting Docker containers...")
                self._run_docker_compose_commands()

                self.containers_started = True
                print("Docker containers started successfully")

                # Wait for services to be ready
                self.wait_for_services()

            finally:
                os.chdir(original_cwd)

        except Exception as e:
            print(f"Docker setup failed: {e}")
            raise

    def _run_docker_compose_commands(self):
        """Run docker-compose commands using subprocess (fallback)."""
        import subprocess

        compose_file = "docker/dev/compose.yml"

        print("Building Docker containers...")
        build_cmd = ["docker", "compose", "-f", compose_file, "build"]
        result = subprocess.run(build_cmd, capture_output=True, text=True, timeout=300)

        if result.returncode != 0:
            raise Exception(f"Docker build failed: {result.stderr}")

        print("Starting Docker containers...")
        up_cmd = ["docker", "compose", "-f", compose_file, "up", "-d"]
        result = subprocess.run(up_cmd, capture_output=True, text=True, timeout=120)

        if result.returncode != 0:
            raise Exception(f"Docker up failed: {result.stderr}")
            
    def wait_for_services(self):
        """Wait for Docker services to be ready."""
        print("Waiting for services to be ready...")

        # Wait for database to be ready
        max_attempts = 30
        for attempt in range(max_attempts):
            try:
                # Check if backend container is running and database is accessible
                self._run_docker_exec_command([
                    "python", "manage.py", "check", "--database", "default"
                ])
                print("Database is ready!")
                break

            except Exception:
                pass

            print(f"Waiting for database... (attempt {attempt + 1}/{max_attempts})")
            time.sleep(2)
        else:
            raise Exception("Database failed to become ready within timeout")

    def run_django_migrations(self):
        """Run Django database migrations."""
        print("Running Django migrations...")

        try:
            self._run_docker_exec_command([
                "python", "manage.py", "migrate"
            ])
            print("Django migrations completed successfully")
        except Exception as e:
            raise Exception(f"Django migrations failed: {e}")

    def create_superuser(self):
        """Create Django admin superuser."""
        print("Creating Django superuser...")

        try:
            # Create superuser using environment variables
            env_vars = {
                'DJANGO_SUPERUSER_USERNAME': 'admin',
                'DJANGO_SUPERUSER_EMAIL': '<EMAIL>',
                'DJANGO_SUPERUSER_PASSWORD': 'admin123'
            }

            self._run_docker_exec_command([
                "python", "manage.py", "createsuperuser", "--noinput"
            ], env_vars=env_vars)

            print("Django superuser created successfully")
        except Exception as e:
            raise Exception(f"Superuser creation failed: {e}")

    def _run_docker_exec_command(self, command, env_vars=None):
        """Execute a command in the Docker container."""
        import subprocess

        # Build docker exec command
        cmd = ["docker", "exec"]

        # Add environment variables if provided
        if env_vars:
            for key, value in env_vars.items():
                cmd.extend(["-e", f"{key}={value}"])

        # Add container name and command
        cmd.append(f"{self.project_name}_backend")
        cmd.extend(command)

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

        if result.returncode != 0:
            raise Exception(f"Command failed: {result.stderr}")

        return result.stdout
        
    def verify_admin_interface(self):
        """Verify Django admin interface is accessible."""
        print("Verifying Django admin interface...")
        
        # Determine the admin URL based on nginx configuration
        if self.containers_started:
            # Try nginx URL first (if nginx is enabled)
            admin_urls = [
                f"http://backend.{self.project_name}.localhost/admin/",
                "http://localhost/admin/",
                "http://localhost:3003/admin/"  # Direct backend access
            ]
        else:
            admin_urls = ["http://localhost:3003/admin/"]
            
        success = False
        for url in admin_urls:
            try:
                print(f"Trying admin URL: {url}")
                response = requests.get(url, timeout=10)
                
                if response.status_code == 200:
                    if "Django administration" in response.text or "admin" in response.text.lower():
                        print(f"✓ Admin interface accessible at: {url}")
                        success = True
                        break
                    else:
                        print(f"✗ URL responded but doesn't appear to be admin interface: {url}")
                else:
                    print(f"✗ HTTP {response.status_code} from {url}")
                    
            except requests.exceptions.RequestException as e:
                print(f"✗ Failed to connect to {url}: {e}")
                
        if not success:
            raise Exception("Django admin interface is not accessible at any expected URL")
            
        print("Django admin interface verification completed successfully")

    def cleanup_resources(self):
        """Clean up Docker containers, virtual environment, and test files."""
        print("Cleaning up resources...")

        if self.containers_started and self.project_dir:
            # Stop and remove containers
            original_cwd = os.getcwd()
            os.chdir(self.project_dir)

            try:
                self._cleanup_docker_containers()
            except Exception as e:
                print(f"Warning: Error during Docker cleanup: {e}")
            finally:
                os.chdir(original_cwd)

        # Remove test directory (includes virtual environment)
        if self.test_dir and os.path.exists(self.test_dir):
            try:
                shutil.rmtree(self.test_dir)
                print(f"Test directory removed: {self.test_dir}")
            except Exception as e:
                print(f"Warning: Could not remove test directory: {e}")

        print("Cleanup completed")

    def _cleanup_docker_containers(self):
        """Clean up Docker containers and images."""
        import subprocess

        compose_file = "docker/dev/compose.yml"

        print("Stopping Docker containers...")
        down_cmd = ["docker", "compose", "-f", compose_file, "down", "-v", "--remove-orphans"]
        subprocess.run(down_cmd, capture_output=True, text=True, timeout=60)

        # Remove images
        print("Removing Docker images...")
        rmi_cmd = ["docker", "rmi", f"{self.project_name}_backend", "-f"]
        subprocess.run(rmi_cmd, capture_output=True, text=True, timeout=30)

    def run_full_test(self):
        """Run the complete Django project test."""
        print(f"Starting Django project test for '{self.project_name}'")
        print("=" * 60)

        try:
            # Setup and project generation
            self.setup_test_environment()
            self.generate_django_project_with_cookiecutter()

            # Docker environment and Django setup
            self.setup_docker_environment()
            self.run_django_migrations()
            self.create_superuser()

            # Verification
            self.verify_admin_interface()

            print("=" * 60)
            print("✓ All tests passed successfully!")
            print(f"✓ Django project '{self.project_name}' created and verified")
            print("✓ Virtual environment created and configured")
            print("✓ Docker environment setup and running")
            print("✓ Database migrations completed")
            print("✓ Admin superuser created")
            print("✓ Admin interface accessible and responding")

            return True

        except Exception as e:
            print("=" * 60)
            print(f"✗ Test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

        finally:
            self.cleanup_resources()


def check_docker_availability():
    """Check if Docker and Docker Compose are available."""
    import subprocess

    try:
        subprocess.run(["docker", "compose", "version"],
                      check=True, capture_output=True, text=True)
        print("Docker Compose is available")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("Warning: docker compose not found. Please install Docker and Docker Compose.")
        return False


def main():
    """Main test execution function."""
    print("Django Cookiecutter Template Test")
    print("=" * 60)

    # Check Docker availability
    if not check_docker_availability():
        print("Docker is required for this test. Exiting.")
        sys.exit(1)

    # Run the test
    test = DjangoProjectTest("blog")
    success = test.run_full_test()

    if success:
        print("\n🎉 Test completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Test failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
