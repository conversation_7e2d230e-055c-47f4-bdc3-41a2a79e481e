#!/usr/bin/env python3
"""
Simple test for Django project creation.
"""

import os
import sys
import time
import shutil
import tempfile
import subprocess
import requests


def test_blog_project():
    """Test creating and running a blog project."""
    print("Starting simple Django blog project test...")
    
    # Create temporary directory
    test_dir = tempfile.mkdtemp(prefix="django_test_blog_")
    project_dir = os.path.join(test_dir, "blog")
    
    try:
        print(f"Test directory: {test_dir}")
        
        # Copy template and create blog project
        template_dir = os.path.join(os.getcwd(), "{{cookiecutter.project_slug}}")
        
        if not os.path.exists(template_dir):
            raise Exception(f"Template directory not found: {template_dir}")
        
        print("Copying template...")
        shutil.copytree(template_dir, project_dir)
        
        # Simple variable replacement in key files
        print("Processing template variables...")
        
        # Process compose.yml
        compose_file = os.path.join(project_dir, "docker/dev/compose.yml")
        with open(compose_file, 'r') as f:
            content = f.read()
        
        content = content.replace('{{cookiecutter.project_slug}}', 'blog')
        content = content.replace('{{cookiecutter.postgres_version}}', '16')
        content = content.replace('{% if cookiecutter.use_nginx == \'y\' %}', '')
        content = content.replace('{% endif %}', '')
        
        with open(compose_file, 'w') as f:
            f.write(content)
        
        # Process Dockerfile
        dockerfile = os.path.join(project_dir, "docker/dev/blog_backend.Dockerfile")
        dockerfile_template = os.path.join(project_dir, "docker/dev/{{cookiecutter.project_slug}}_backend.Dockerfile")
        
        if os.path.exists(dockerfile_template):
            with open(dockerfile_template, 'r') as f:
                content = f.read()
            
            content = content.replace('{{cookiecutter.python_version}}', '3.12')
            content = content.replace('{{cookiecutter.project_slug}}', 'blog')
            content = content.replace('{{cookiecutter.project_slug.upper()}}', 'BLOG')
            
            with open(dockerfile, 'w') as f:
                f.write(content)
            
            os.remove(dockerfile_template)
        
        # Process entrypoint script
        entrypoint_template = os.path.join(project_dir, "docker/dev/{{cookiecutter.project_slug}}_backend-entrypoint.sh")
        entrypoint = os.path.join(project_dir, "docker/dev/blog_backend-entrypoint.sh")
        
        if os.path.exists(entrypoint_template):
            with open(entrypoint_template, 'r') as f:
                content = f.read()
            
            content = content.replace('{{cookiecutter.project_slug}}', 'blog')
            
            with open(entrypoint, 'w') as f:
                f.write(content)
            
            os.chmod(entrypoint, 0o755)
            os.remove(entrypoint_template)
        
        # Process nginx config
        nginx_template = os.path.join(project_dir, "docker/dev/{{cookiecutter.project_slug}}_ws.Dockerfile")
        nginx_dockerfile = os.path.join(project_dir, "docker/dev/blog_ws.Dockerfile")
        
        if os.path.exists(nginx_template):
            shutil.copy2(nginx_template, nginx_dockerfile)
            os.remove(nginx_template)
        
        # Process nginx conf
        nginx_conf = os.path.join(project_dir, "docker/dev/nginx.conf")
        if os.path.exists(nginx_conf):
            with open(nginx_conf, 'r') as f:
                content = f.read()
            
            content = content.replace('{{cookiecutter.project_slug}}', 'blog')
            content = content.replace('{{cookiecutter.domain_pattern}}', '*.blog.localhost')
            
            with open(nginx_conf, 'w') as f:
                f.write(content)
        
        # Process Django settings
        settings_file = os.path.join(project_dir, "backend/django/project/settings/base.py")
        if os.path.exists(settings_file):
            with open(settings_file, 'r') as f:
                content = f.read()
            
            content = content.replace('{{cookiecutter.project_name}}', 'Blog Project')
            content = content.replace('{{cookiecutter.project_slug}}', 'blog')
            content = content.replace('{{cookiecutter.django_version}}', '5.1')
            
            with open(settings_file, 'w') as f:
                f.write(content)
        
        # Process dev settings
        dev_settings = os.path.join(project_dir, "backend/django/project/settings/dev.py")
        if os.path.exists(dev_settings):
            with open(dev_settings, 'r') as f:
                content = f.read()
            
            content = content.replace('{{cookiecutter.project_name}}', 'Blog Project')
            content = content.replace('{{cookiecutter.project_slug}}', 'blog')
            
            with open(dev_settings, 'w') as f:
                f.write(content)
        
        # Process Django app files
        app_dir = os.path.join(project_dir, "backend/django/blog")
        app_template_dir = os.path.join(project_dir, "backend/django/{{cookiecutter.project_slug}}")
        
        if os.path.exists(app_template_dir):
            shutil.move(app_template_dir, app_dir)
            
            # Process apps.py
            apps_file = os.path.join(app_dir, "apps.py")
            if os.path.exists(apps_file):
                with open(apps_file, 'r') as f:
                    content = f.read()
                
                content = content.replace('{{cookiecutter.project_slug}}', 'blog')
                content = content.replace('{{cookiecutter.project_slug.title().replace(\'_\', \'\')}}', 'Blog')
                
                with open(apps_file, 'w') as f:
                    f.write(content)
        
        # Process other Django files
        django_files = [
            "backend/django/project/urls.py",
            "backend/django/project/wsgi.py", 
            "backend/django/project/asgi.py"
        ]
        
        for file_path in django_files:
            full_path = os.path.join(project_dir, file_path)
            if os.path.exists(full_path):
                with open(full_path, 'r') as f:
                    content = f.read()
                
                content = content.replace('{{cookiecutter.project_name}}', 'Blog Project')
                
                with open(full_path, 'w') as f:
                    f.write(content)
        
        # Process requirements
        req_file = os.path.join(project_dir, "requirements.txt")
        if os.path.exists(req_file):
            with open(req_file, 'r') as f:
                content = f.read()
            
            content = content.replace('{{cookiecutter.django_version}}', '5.1')
            
            with open(req_file, 'w') as f:
                f.write(content)
        
        print("Template processing completed")
        
        # Change to project directory for Docker commands
        original_cwd = os.getcwd()
        os.chdir(project_dir)
        
        try:
            # Build and start containers
            print("Building Docker containers...")
            build_cmd = ["docker", "compose", "-f", "docker/dev/compose.yml", "build"]
            result = subprocess.run(build_cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode != 0:
                print(f"Build stdout: {result.stdout}")
                print(f"Build stderr: {result.stderr}")
                raise Exception(f"Docker build failed")
            
            print("Starting Docker containers...")
            up_cmd = ["docker", "compose", "-f", "docker/dev/compose.yml", "up", "-d"]
            result = subprocess.run(up_cmd, capture_output=True, text=True, timeout=120)
            
            if result.returncode != 0:
                print(f"Up stdout: {result.stdout}")
                print(f"Up stderr: {result.stderr}")
                raise Exception(f"Docker up failed")
            
            print("Containers started successfully")

            # Wait for services
            print("Waiting for services to be ready...")
            time.sleep(15)

            # Run migrations
            print("Running Django migrations...")
            migrate_cmd = ["docker", "exec", "blog_backend", "python", "manage.py", "migrate"]
            result = subprocess.run(migrate_cmd, capture_output=True, text=True, timeout=60)

            if result.returncode != 0:
                print(f"Migration stdout: {result.stdout}")
                print(f"Migration stderr: {result.stderr}")
                print("Warning: Migrations failed, but continuing...")
            else:
                print("Migrations completed successfully")

            # Create superuser
            print("Creating Django superuser...")
            superuser_cmd = [
                "docker", "exec", "-e", "DJANGO_SUPERUSER_USERNAME=admin",
                "-e", "DJANGO_SUPERUSER_EMAIL=<EMAIL>",
                "-e", "DJANGO_SUPERUSER_PASSWORD=admin123",
                "blog_backend", "python", "manage.py", "createsuperuser", "--noinput"
            ]
            result = subprocess.run(superuser_cmd, capture_output=True, text=True, timeout=30)

            if result.returncode != 0:
                print(f"Superuser stdout: {result.stdout}")
                print(f"Superuser stderr: {result.stderr}")
                print("Warning: Superuser creation failed, but continuing...")
            else:
                print("Superuser created successfully")

            # Check if admin interface is accessible
            print("Testing admin interface...")
            admin_url = "http://localhost:3003/admin/"
            
            max_attempts = 10
            for attempt in range(max_attempts):
                try:
                    response = requests.get(admin_url, timeout=5)
                    if response.status_code == 200:
                        print(f"✓ Admin interface accessible at: {admin_url}")
                        print("✓ Test completed successfully!")
                        return True
                except requests.exceptions.RequestException:
                    pass
                
                print(f"Waiting for admin interface... (attempt {attempt + 1}/{max_attempts})")
                time.sleep(3)
            
            print("✗ Admin interface not accessible")
            return False
            
        finally:
            # Cleanup
            print("Cleaning up...")
            down_cmd = ["docker", "compose", "-f", "docker/dev/compose.yml", "down", "-v"]
            subprocess.run(down_cmd, capture_output=True, text=True, timeout=60)
            os.chdir(original_cwd)
            
    except Exception as e:
        print(f"✗ Test failed: {e}")
        return False
        
    finally:
        # Remove test directory
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
            print(f"Test directory removed: {test_dir}")


if __name__ == "__main__":
    print("Simple Django Project Test")
    print("=" * 50)
    
    success = test_blog_project()
    
    if success:
        print("\n🎉 Test completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Test failed!")
        sys.exit(1)
